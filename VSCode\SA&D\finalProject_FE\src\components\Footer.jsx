import React from "react";
import "bootstrap/dist/css/bootstrap.min.css";

function Footer() {
  return (
    <footer className="bg-dark text-white py-5 mt-auto">
      <div className="container">
        <div className="row">
          {/* Cột 1: V<PERSON> chúng tôi */}
          <div className="col-md-3">
            <h5 className="fw-bold">Về chúng tôi</h5>
            <p className="">
              Chúng tôi cung cấp sản phẩm chất lượng với giá tốt nhất. Hãy mua sắm cùng chúng tôi ngay hôm nay!
            </p>
          </div>

          {/* Cột 2: <PERSON>ê<PERSON> kết nhanh */}
          <div className="col-md-3">
            <h5 className="fw-bold">Liên kết nhanh</h5>
            <ul className="list-unstyled text-muted">
              <li><a href="#" className="text-white text-decoration-none">Trang chủ</a></li>
              <li><a href="#" className="text-white text-decoration-none">Sản phẩm</a></li>
              <li><a href="#" className="text-white text-decoration-none">Khuyến mãi</a></li>
              <li><a href="#" className="text-white text-decoration-none">Liên hệ</a></li>
            </ul>
          </div>

          {/* Cột 3: Hỗ trợ khách hàng */}
          <div className="col-md-3">
            <h5 className="fw-bold">Hỗ trợ khách hàng</h5>
            <ul className="list-unstyled text-muted">
              <li><a href="#" className="text-white text-decoration-none">Chính sách đổi trả</a></li>
              <li><a href="#" className="text-white text-decoration-none">Chính sách bảo hành</a></li>
              <li><a href="#" className="text-white text-decoration-none">Phương thức thanh toán</a></li>
              <li><a href="#" className="text-white text-decoration-none">Vận chuyển</a></li>
            </ul>
          </div>

          {/* Cột 4: Liên hệ */}
          <div className="col-md-3">
            <h5 className="fw-bold">Liên hệ</h5>
            <p className="d-flex align-items-center gap-2">
              <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M288 0c-69.59 0-126 56.41-126 126 0 56.26 82.35 158.8 113.9 196.02 6.39 7.54 17.82 7.54 24.2 0C331.65 284.8 414 182.26 414 126 414 56.41 357.59 0 288 0zm0 168c-23.2 0-42-18.8-42-42s18.8-42 42-42 42 18.8 42 42-18.8 42-42 42zM20.12 215.95A32.006 32.006 0 0 0 0 245.66v250.32c0 11.32 11.43 19.06 21.94 14.86L160 448V214.92c-8.84-15.98-16.07-31.54-21.25-46.42L20.12 215.95zM288 359.67c-14.07 0-27.38-6.18-36.51-16.96-19.66-23.2-40.57-49.62-59.49-76.72v182l192 64V266c-18.92 27.09-39.82 53.52-59.49 76.72-9.13 10.77-22.44 16.95-36.51 16.95zm266.06-198.51L416 224v288l139.88-55.95A31.996 31.996 0 0 0 576 426.34V176.02c0-11.32-11.43-19.06-21.94-14.86z"></path></svg>
              123 Đường ABC, Hà Nội
            </p>
            <p className="d-flex align-items-center gap-2">
              <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M164.9 24.6c-7.7-18.6-28-28.5-47.4-23.2l-88 24C12.1 30.2 0 46 0 64C0 311.4 200.6 512 448 512c18 0 33.8-12.1 38.6-29.5l24-88c5.3-19.4-4.6-39.7-23.2-47.4l-96-40c-16.3-6.8-35.2-2.1-46.3 11.6L304.7 368C234.3 334.7 177.3 277.7 144 207.3L193.3 167c13.7-11.2 18.4-30 11.6-46.3l-40-96z"></path></svg>
              0987 654 321
            </p>
            <p className="d-flex align-items-center gap-2">
              <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 24 24" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0z"></path><path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4-8 5-8-5V6l8 5 8-5v2z"></path></svg>
              <EMAIL>
            </p>
            <div className="d-flex gap-3">
              <a href="#" className="text-white fs-5"><i className="fab fa-facebook"></i></a>
              <a href="#" className="text-white fs-5"><i className="fab fa-instagram"></i></a>
              <a href="#" className="text-white fs-5"><i className="fab fa-twitter"></i></a>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="text-center border-top pt-3 mt-4 ">
          © 2025 DoorShop. Tất cả các quyền được bảo lưu.
        </div>
      </div>
    </footer>
  );
}

export default Footer;
