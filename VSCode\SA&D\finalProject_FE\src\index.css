body {
  padding: 0;
  margin: 0;
  width: 100%;
  height: 100%;
}

#root {
  margin: 0;
  padding: 0;
  border: 0;
}

/* ===== MODERN MEDICAL THEME ===== */

/* Root Variables */
:root {
  /* Primary Colors - Medical Green Palette */
  --primary-green: #00B894;
  --primary-mint: #00CEC9;
  --primary-light: #A8E6CF;
  --primary-dark: #00A085;

  /* Secondary Colors - Clean Blues */
  --secondary-blue: #0984E3;
  --secondary-light: #74B9FF;
  --secondary-dark: #0652DD;

  /* Accent Colors */
  --accent-orange: #FDCB6E;
  --accent-red: #E17055;
  --accent-purple: #A29BFE;

  /* Neutral Palette */
  --white: #FFFFFF;
  --gray-50: #F8FAFC;
  --gray-100: #F1F5F9;
  --gray-200: #E2E8F0;
  --gray-300: #CBD5E1;
  --gray-400: #94A3B8;
  --gray-500: #64748B;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1E293B;
  --gray-900: #0F172A;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-mint) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--secondary-blue) 0%, var(--secondary-light) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-orange) 0%, var(--accent-red) 100%);
  --gradient-bg: linear-gradient(135deg, #F8FAFC 0%, #E2E8F0 100%);

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.15);

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-primary);
  background: var(--gradient-bg);
  color: var(--gray-700);
  line-height: 1.6;
  min-height: 100vh;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-heading);
  font-weight: 600;
  line-height: 1.2;
  color: var(--gray-800);
}

/* ===== HEADER / NAVBAR ===== */
.modern-navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-lg);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.navbar-brand h1 {
  font-family: var(--font-heading);
  font-weight: 700;
  font-size: 1.75rem;
  margin: 0;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.navbar-brand .text-primary {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.navbar-nav .nav-link {
  font-weight: 500;
  color: var(--gray-600) !important;
  padding: 0.75rem 1.25rem !important;
  margin: 0 0.25rem;
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
  position: relative;
}

.navbar-nav .nav-link:hover {
  color: var(--primary-green) !important;
  background: rgba(0, 184, 148, 0.1);
  transform: translateY(-2px);
}

.navbar-nav .nav-link.active {
  color: var(--white) !important;
  background: var(--gradient-primary);
  box-shadow: var(--shadow-md);
}

/* ===== GLASSMORPHISM CARDS ===== */
.glass-card {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-glass);
  transition: all 0.3s ease;
  overflow: hidden;
}

.glass-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
  background: rgba(255, 255, 255, 0.95);
}

.product-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-2xl);
  padding: 1.5rem;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
}

.product-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: var(--shadow-xl);
  background: rgba(255, 255, 255, 0.95);
}

.product-card:hover::before {
  opacity: 1;
}

.card-image {
  border-radius: var(--radius-lg);
  overflow: hidden;
  position: relative;
}

.card-image img {
  transition: transform 0.4s ease;
}

.product-card:hover .card-image img {
  transform: scale(1.05);
}

/* ===== MODERN BUTTONS ===== */
.btn-modern {
  font-weight: 600;
  padding: 0.75rem 2rem;
  border-radius: var(--radius-lg);
  border: none;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.875rem;
  position: relative;
  overflow: hidden;
}

.btn-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.btn-modern:hover::before {
  left: 100%;
}

.btn-primary-modern {
  background: var(--gradient-primary);
  color: var(--white);
  box-shadow: var(--shadow-md);
}

.btn-primary-modern:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  background: var(--gradient-primary);
}

.btn-secondary-modern {
  background: var(--gradient-secondary);
  color: var(--white);
  box-shadow: var(--shadow-md);
}

.btn-add-modern {
  background: var(--gradient-secondary);
  color: var(--white);
}

.btn-edit-modern {
  background: var(--gradient-accent);
  color: var(--white);
}

.btn-delete-modern {
  background: linear-gradient(135deg, var(--accent-red) 0%, #D63384 100%);
  color: var(--white);
}

.btn-add-modern:hover,
.btn-edit-modern:hover,
.btn-delete-modern:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* ===== MODERN TABLES ===== */
.modern-table {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(15px);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.modern-table thead {
  background: var(--gradient-primary);
  color: var(--white);
}

.modern-table thead th {
  font-weight: 600;
  padding: 1.25rem 1rem;
  border: none;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.875rem;
}

.modern-table tbody tr {
  border: none;
  transition: all 0.3s ease;
}

.modern-table tbody tr:nth-child(odd) {
  background: rgba(0, 184, 148, 0.05);
}

.modern-table tbody tr:hover {
  background: rgba(0, 184, 148, 0.15);
  transform: scale(1.01);
}

.modern-table tbody td {
  padding: 1rem;
  border: none;
  vertical-align: middle;
}

/* ===== MODERN FORMS ===== */
.form-floating-modern {
  position: relative;
  margin-bottom: 1.5rem;
}

.form-floating-modern .form-control {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: 1rem;
  transition: all 0.3s ease;
}

.form-floating-modern .form-control:focus {
  border-color: var(--primary-green);
  box-shadow: 0 0 0 0.25rem rgba(0, 184, 148, 0.25);
  background: rgba(255, 255, 255, 0.95);
}

/* ===== PAGINATION ===== */
.pagination-modern .page-item .page-link {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: var(--gray-600);
  margin: 0 0.25rem;
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
}

.pagination-modern .page-item .page-link:hover {
  background: var(--gradient-primary);
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.pagination-modern .page-item.active .page-link {
  background: var(--gradient-primary);
  border-color: var(--primary-green);
  color: var(--white);
  box-shadow: var(--shadow-md);
}

/* ===== FOOTER ===== */
.modern-footer {
  background: linear-gradient(135deg, var(--gray-800) 0%, var(--gray-900) 100%);
  color: var(--gray-300);
  position: relative;
  overflow: hidden;
}

.modern-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}

.footer-link {
  color: var(--gray-300);
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
}

.footer-link:hover {
  color: var(--primary-mint);
  transform: translateX(5px);
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* ===== HERO SECTION ===== */
.hero-section {
  background: var(--gradient-primary);
  color: var(--white);
  padding: 4rem 0;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.5;
}

.hero-content {
  position: relative;
  z-index: 2;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
  .navbar-nav .nav-link {
    padding: 0.5rem 1rem !important;
    margin: 0.25rem 0;
  }

  .product-card {
    margin-bottom: 1.5rem;
  }

  .glass-card {
    margin-bottom: 1rem;
  }
}