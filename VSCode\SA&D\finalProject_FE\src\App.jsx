import { BrowserRouter, Routes, Route } from "react-router-dom";
import Layout from './layouts/layout.jsx';
import Login from './pages/Login.jsx';
import Register from './pages/Register.jsx';
import Home from './pages/Home.jsx';
import ProfilePage from "./pages/Profile.jsx";
import AppointmentAdd from "./pages/AppointmentAdd.jsx";
import AppointmentHistory from "./pages/Appointment.jsx";
import Availability from "./pages/Availability.jsx";
import PrescriptionForm from "./pages/PrescriptionAdd.jsx";
import PrescriptionList from "./pages/Prescription.jsx";
import CategoryManager from "./pages/Category.jsx";
import CategoryForm from "./pages/CategoryForm.jsx";
import MedicineManager from "./pages/Medicine.jsx";
import MedicineForm from "./pages/MedicineForm.jsx";
import LabForm from "./pages/LabAdd.jsx";
import LabList from "./pages/Lab.jsx";
import LabResult from "./pages/LabResult.jsx";
import RecordForm from "./pages/RecordAdd.jsx";
import RecordList from "./pages/Record.jsx";
import Chatbot from "./pages/Chatbot.jsx";

function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<Layout/>}>
          <Route path="/" element={<Home/>}></Route>
          <Route path="/profile" element={<ProfilePage/>}></Route>
          <Route path="/appointment/add" element={<AppointmentAdd />} />
          <Route path="/appointment" element={<AppointmentHistory />} />

          <Route path="/available" element={<Availability />} />

          <Route path="/prescription" element={<PrescriptionList />} />
          <Route path="/prescription/add" element={<PrescriptionForm />} />

          <Route path="/category" element={<CategoryManager />} />
          <Route path="/category/add" element={<CategoryForm />} /> {/* Route thêm mới */}
          <Route path="/category/edit/:id" element={<CategoryForm />} /> {/* Route chỉnh sửa */}

          <Route path="/medicine" element={<MedicineManager />} />
          <Route path="/medicine/add" element={<MedicineForm />} />
          <Route path="/medicine/edit/:id" element={<MedicineForm />} />
          
          <Route path="/lab" element={<LabList />} />
          <Route path="/lab/add" element={<LabForm />} />
          <Route path="/lab/result/:id" element={<LabResult />} />

          <Route path="/record" element={<RecordList />} />
          <Route path="/record/add" element={<RecordForm />} />

          <Route path="/chat" element={<Chatbot />} />

        </Route>
        <Route path="/login" element={<Login/>}></Route>
        <Route path="/register" element={<Register/>}></Route>
      </Routes>
    
    </BrowserRouter>
  )
}

export default App